import { ref, reactive } from "vue";
import { Buffer } from "buffer";
import { pack, PacketReassembler, DataType } from "@/utils/ble-protocol";

// 设备信息接口
interface DeviceInfo {
  name: string;
  id: string;
  serviceId: string;
}

// 日志接口
interface DebugLog {
  time: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
}

// ===== BLE 常量 =====
const SERVICE_UUID = "0000ffff-1000-0000-2802-018833401286";
const CHAR_UUID = "0000ffff-1001-0000-2802-018833401286";
// 授权
const CHAR_UUID_TOKEN = "0000ffff-1002-0000-2802-018833401286";
const CHAR_UUID_TERMINAL = "0000ffff-1002-0000-2802-018833401286";
const CHAR_UUID_NETWORK = "0000ffff-1004-0001-2802-018833401286";

// --- 模拟环境 ---
const ATT_MTU = 200;

export function useBLE() {
  const isConnected = ref(false);
  const deviceInfo = reactive<DeviceInfo>({
    name: "",
    id: "",
    serviceId: "",
  });
  const foundDevices = ref<any[]>([]);
  const debugLogs = ref<DebugLog[]>([]);
  const showDeviceModal = ref(false);
  let notifyInited = false;

  // 添加日志
  const addLog = (message: string, type: DebugLog["type"] = "info") => {
    const now = new Date();
    const time = `${now.getHours().toString().padStart(2, "0")}:${now
      .getMinutes()
      .toString()
      .padStart(2, "0")}:${now.getSeconds().toString().padStart(2, "0")}`;

    debugLogs.value.push({
      time,
      message,
      type,
    });
  };

  // 扫描设备
  const scanDevice = () => {
    addLog("开始扫描设备...", "info");
    foundDevices.value = [];

    uni.showLoading({ title: "扫描中..." });

    // 初始化蓝牙模块
    uni.openBluetoothAdapter({
      success: () => {
        addLog("蓝牙适配器初始化成功", "success");
        // 开始搜寻附近的蓝牙外围设备。此操作比较耗费系统资源，请在搜索并连接到设备后调用 uni.stopBluetoothDevicesDiscovery 方法停止搜索。
        uni.startBluetoothDevicesDiscovery({
          services: [SERVICE_UUID],
          allowDuplicatesKey: true,
          success: () => addLog("开始搜索设备", "info"),
          fail: err => addLog("搜索设备失败: " + JSON.stringify(err), "error"),
        });

        // 监听寻找到新设备的事件
        uni.onBluetoothDeviceFound(res => {
          res.devices.forEach(dev => {
            if (!(dev.name || dev.localName)) return;
            if (foundDevices.value.find(d => d.deviceId === dev.deviceId)) return;
            foundDevices.value.push(dev);
            addLog(`发现设备: ${dev.name || dev.localName} (${dev.deviceId})`, "success");
          });
        });
      },
      fail: err => {
        addLog("蓝牙适配器初始化失败: " + JSON.stringify(err), "error");
        uni.hideLoading();
      },
    });

    setTimeout(() => {
      // 停止搜寻附近的蓝牙外围设备
      uni.stopBluetoothDevicesDiscovery();
      uni.hideLoading();
      addLog(`扫描完成，共发现 ${foundDevices.value.length} 个设备`, "info");

      if (foundDevices.value.length > 0) {
        showDeviceModal.value = true;
      } else {
        addLog("未发现任何设备", "warning");
      }
    }, 2000);
  };

  // 连接设备
  const connectDevice = (deviceId: string) => {
    if (!deviceId) {
      addLog("设备 ID 无效", "error");
      return;
    }

    if (isConnected.value) {
      addLog("设备已连接，无需重复连接", "warning");
      return;
    }

    addLog("正在连接设备...", "info");
    uni.showLoading({ title: "连接中..." });

    // 连接低功耗蓝牙设备
    uni.createBLEConnection({
      deviceId,
      success: () => {
        addLog("BLE 连接成功", "success");
        isConnected.value = true;

        uni.setBLEMTU({
          deviceId,
          mtu: 50,
          success: () => {
            addLog("MTU 设置成功", "success");
          },
          fail: err => {
            addLog("MTU 设置失败: " + JSON.stringify(err), "error");
          },
        });

        // 监听低功耗蓝牙连接状态的改变事件。包括开发者主动连接或断开连接，设备丢失，连接异常断开等等
        uni.onBLEConnectionStateChange(state => {
          isConnected.value = state.connected;
          if (!state.connected) {
            addLog("设备已断开连接", "error");
          }
        });

        setTimeout(() => {
          // 获取蓝牙设备所有服务(service)。
          uni.getBLEDeviceServices({
            deviceId,
            success: srvRes => {
              addLog(`获取蓝牙设备所有服务 ${JSON.stringify(srvRes)}`, "success");
              const srv = srvRes.services.find(s => s.uuid.toLowerCase() === SERVICE_UUID);
              if (!srv) {
                addLog("未找到目标 Service", "error");
                return;
              }

              // 获取蓝牙设备某个服务中所有特征值
              uni.getBLEDeviceCharacteristics({
                deviceId,
                serviceId: srv.uuid,
                success: chRes => {
                  addLog(`获取蓝牙设备某个服务中所有特征值 ${JSON.stringify(chRes)}`, "success");
                  // 启用低功耗蓝牙设备特征值变化时的 notify 功能，订阅特征值。
                  uni.notifyBLECharacteristicValueChange({
                    state: true,
                    deviceId,
                    serviceId: srv.uuid,
                    characteristicId: CHAR_UUID_TERMINAL,
                    success: () => {
                      notifyInited = true;
                      addLog("已开启数据监听_终端CHAR_UUID_TERMINAL", "success");
                    },
                    fail: err =>
                      addLog(
                        "开启监听失败_终端CHAR_UUID_TERMINAL: " + JSON.stringify(err),
                        "error"
                      ),
                  });

                  // 启用低功耗蓝牙设备特征值变化时的 notify 功能，订阅特征值。
                  uni.notifyBLECharacteristicValueChange({
                    state: true,
                    deviceId,
                    serviceId: srv.uuid,
                    characteristicId: CHAR_UUID_NETWORK,
                    success: () => {
                      notifyInited = true;
                      addLog("已开启数据监听_终端CHAR_UUID_NETWORK", "success");
                    },
                    fail: err =>
                      addLog("开启监听失败_终端CHAR_UUID_NETWORK: " + JSON.stringify(err), "error"),
                  });
                },
                fail: err => addLog("获取特征失败: " + JSON.stringify(err), "error"),
              });
            },
            fail: err => addLog("获取服务失败: " + JSON.stringify(err), "error"),
          });
        }, 500);

        uni.hideLoading();
        uni.showToast({ title: "连接成功", icon: "success" });
      },
      fail: err => {
        uni.hideLoading();
        addLog("连接失败: " + JSON.stringify(err), "error");
      },
    });
  };

  // 选择并连接设备
  const selectAndConnectDevice = (device: any) => {
    showDeviceModal.value = false;
    deviceInfo.name = device.name || device.localName;
    deviceInfo.id = device.deviceId;
    deviceInfo.serviceId = device.advertisServiceUUIDs[0] || "无特征值";
    addLog(`已选择设备: ${deviceInfo.name}`, "info");
    connectDevice(device.deviceId);
  };

  // 断开设备连接
  const disconnectDevice = () => {
    if (!isConnected.value) {
      addLog("设备未连接", "warning");
      return;
    }

    addLog("正在断开连接...", "info");
    uni.closeBLEConnection({
      deviceId: deviceInfo.id,
      success: () => {
        addLog("设备已成功断开", "success");
        isConnected.value = false;
        // 重置设备信息
        deviceInfo.id = "";
        deviceInfo.name = "";
        deviceInfo.serviceId = "";
      },
      fail: err => {
        addLog("断开连接失败: " + JSON.stringify(err), "error");
      },
    });
  };

  // 接收消息 监听低功耗蓝牙设备的特征值变化事件。必须先启用 notifyBLECharacteristicValueChange 接口才能接收到设备推送的 notification
  uni.onBLECharacteristicValueChange &&
    uni.onBLECharacteristicValueChange(res => {
      addLog(JSON.stringify(res), "info");
      buildMsg(Buffer.from(res.value));
    });

  // 测试连接
  const testConnection = () => {
    if (!isConnected.value) {
      addLog("请先连接设备", "error");
      uni.showToast({ title: "请先连接设备", icon: "none" });
      return;
    }

    addLog("开始连接测试...", "info");
    uni.getConnectedBluetoothDevices({
      services: [SERVICE_UUID],
      success: () => addLog("连接测试通过", "success"),
      fail: err => addLog("连接测试失败: " + JSON.stringify(err), "error"),
    });
  };
  const reassembler = new PacketReassembler();

  // 字符串转 ArrayBuffer
  const str2ab = (str: string) => {
    // 将内容转换为Buffer
    const jsonDataBuffer = Buffer.from(str);
    console.log(`原始JSON数据大小: ${jsonDataBuffer.length} 字节`);

    // 使用 pack 函数进行打包
    const packetsToSend = pack(jsonDataBuffer, DataType.JSON, ATT_MTU);
    console.log(`数据被分成了 ${packetsToSend.length} 个包进行发送。`);

    packetsToSend.forEach((packet, index) => {
      console.log(`包 #${index}: 大小=${packet.byteLength}, 内容(HEX)=${packet.toString()}`);
      send2Device(packet);
    });
  };

  const buildMsg = (packet: Buffer) => {
    const result = reassembler.addPacket(packet);

    // 3. 检查是否重组完成
    if (result) {
      // 如果 result 不为 null，说明所有包已接收完毕，数据已成功重组
      console.log("✅ [回调函数] 数据重组完成！");
      addLog("组包完成", "success");
      handleCompleteData(result);
    } else {
      // 如果 result 为 null，说明这只是传输过程中的一个分包
      console.log("... [回调函数] 等待下一个包 ...");
    }

    function handleCompleteData(completeData: { type: DataType; data: Buffer }) {
      console.log("\n--- 🎉 开始处理完整数据 🎉 ---");
      console.log(`数据类型: ${DataType[completeData.type]}`);
      console.log(`数据总大小: ${completeData.data.length} 字节`);
      addLog(completeData.data.toString(), "success");
      console.log("--- ✅ 数据处理完毕 ---\n");
    }
  };

  // 发送指令
  const sendCommand = () => {
    if (!isConnected.value) {
      addLog("请先连接设备", "error");
      uni.showToast({ title: "请先连接设备", icon: "none" });
      return;
    }
    if (!notifyInited) {
      addLog("请稍后，正在初始化通知通道", "warning");
      return;
    }

    uni.showModal({
      title: "发送指令",
      editable: true,
      placeholderText: "请输入指令内容",
      success: res => {
        if (!res.confirm || !res.content) return;
        const cmd = res.content.trim();
        if (!cmd) return;

        addLog("发送指令: " + cmd, "info");

        str2ab(cmd);
      },
    });
  };

  const send2Device = (packet: Buffer) => {
    uni.writeBLECharacteristicValue({
      deviceId: deviceInfo.id,
      serviceId: SERVICE_UUID,
      characteristicId: CHAR_UUID_TERMINAL,
      value: packet.buffer as any, // Buffer转ArrayBuffer
      success: () => {
        addLog("指令发送成功", "success");
      },
      fail: err => addLog("指令发送失败: " + JSON.stringify(err), "error"),
    });
  };

  /**
   * 写入蓝牙特征值 - 支持大数据分包写入
   * @param characteristicId 蓝牙特征值的 uuid
   * @param data 要写入的数据 (string 或 Buffer)
   * @param dataType 数据类型，默认为 JSON
   * @returns Promise<boolean> 写入是否成功
   */
  const writeBLECharacteristic = async (
    characteristicId: string,
    data: string | Buffer,
    dataType: DataType = DataType.JSON
  ): Promise<boolean> => {
    if (!isConnected.value) {
      addLog("请先连接设备", "error");
      return false;
    }

    if (!deviceInfo.id) {
      addLog("设备 ID 无效", "error");
      return false;
    }

    try {
      // 将数据转换为 Buffer
      const dataBuffer = typeof data === "string" ? Buffer.from(data) : data;
      addLog(`准备写入数据，大小: ${dataBuffer.length} 字节`, "info");

      // 使用 pack 函数进行分包
      const packetsToSend = pack(dataBuffer, dataType, ATT_MTU);
      addLog(`数据被分成 ${packetsToSend.length} 个包进行发送`, "info");

      // 逐个发送数据包
      for (let i = 0; i < packetsToSend.length; i++) {
        const packet = packetsToSend[i];
        addLog(`发送包 #${i + 1}/${packetsToSend.length}, 大小: ${packet.byteLength} 字节`, "info");

        const success = await new Promise<boolean>(resolve => {
          // 将 Buffer 转换为 ArrayBuffer
          const arrayBuffer = packet.buffer.slice(
            packet.byteOffset,
            packet.byteOffset + packet.byteLength
          );

          uni.writeBLECharacteristicValue({
            deviceId: deviceInfo.id,
            serviceId: SERVICE_UUID,
            characteristicId,
            value: arrayBuffer as any, // ArrayBuffer转换
            writeType: "write", // 强制回复写
            success: () => {
              addLog(`包 #${i + 1} 发送成功`, "success");
              resolve(true);
            },
            fail: err => {
              addLog(`包 #${i + 1} 发送失败: ${JSON.stringify(err)}`, "error");
              resolve(false);
            },
          });
        });

        if (!success) {
          addLog(`数据写入失败，在第 ${i + 1} 个包时中断`, "error");
          return false;
        }

        // 添加小延迟避免发送过快
        if (i < packetsToSend.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }

      addLog("所有数据包发送完成", "success");
      return true;
    } catch (error) {
      addLog(`写入数据时发生错误: ${error}`, "error");
      return false;
    }
  };

  /**
   * 读取蓝牙特征值 - 支持多次读取数据重组（手动控制模式）
   * @param characteristicId 蓝牙特征值的 uuid
   * @param timeout 读取超时时间（毫秒），默认 10 秒
   * @returns Promise<{type: DataType, data: Buffer} | null> 读取到的完整数据，如果失败返回 null
   */
  const readBLECharacteristic = (characteristicId: string) => {
    if (!isConnected.value) {
      addLog("请先连接设备", "error");
      return null;
    }

    if (!deviceInfo.id) {
      addLog("设备 ID 无效", "error");
      return null;
    }

    uni.readBLECharacteristicValue({
      deviceId: deviceInfo.id,
      serviceId: SERVICE_UUID,
      characteristicId,
      success: (res: any) => {
        console.log(res);
        console.log(JSON.stringify(res));

        addLog(`读取请求 #${JSON.stringify(res)} 成功`, "info");
      },
      fail: err => {
        addLog(`读取请求失败: ${JSON.stringify(err)}`, "error");
      },
    });
  };

  addLog("设备调试工具已启动", "info");
  addLog("请先扫描并连接设备", "info");

  return {
    isConnected,
    deviceInfo,
    foundDevices,
    debugLogs,
    showDeviceModal,
    scanDevice,
    selectAndConnectDevice,
    testConnection,
    sendCommand,
    addLog,
    disconnectDevice,
    writeBLECharacteristic,
    readBLECharacteristic,
  };
}
